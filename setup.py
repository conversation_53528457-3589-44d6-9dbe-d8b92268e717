#!/usr/bin/env python3
"""
ToksNet Setup Script
Automated setup for the ToksNet Skin Condition Classifier
"""

import subprocess
import sys
import os

def run_command(command):
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python 3.7 or higher is required!")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def install_requirements():
    """Install required packages."""
    print("📦 Installing requirements...")
    success, output = run_command("pip install -r requirements.txt")
    if success:
        print("✅ Requirements installed successfully!")
        return True
    else:
        print("❌ Failed to install requirements:")
        print(output)
        return False

def verify_installation():
    """Verify that all packages are installed correctly."""
    print("🔍 Verifying installation...")
    packages = ['streamlit', 'tensorflow', 'PIL', 'numpy']
    
    for package in packages:
        try:
            if package == 'PIL':
                import PIL
            else:
                __import__(package)
            print(f"✅ {package} imported successfully")
        except ImportError:
            print(f"❌ Failed to import {package}")
            return False
    
    return True

def check_model_file():
    """Check if the model file exists."""
    model_path = "best_efficientnet.keras"
    if os.path.exists(model_path):
        print("✅ Model file found: best_efficientnet.keras")
        return True
    else:
        print("❌ Model file not found: best_efficientnet.keras")
        print("Please ensure the model file is in the project directory.")
        return False

def main():
    """Main setup function."""
    print("🚀 ToksNet Setup Script")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        sys.exit(1)
    
    # Verify installation
    if not verify_installation():
        sys.exit(1)
    
    # Check model file
    if not check_model_file():
        print("⚠️  Warning: Model file missing. The app may not work correctly.")
    
    print("\n🎉 Setup completed successfully!")
    print("\nTo run the application:")
    print("  streamlit run app.py")
    print("\nThen open your browser to: http://localhost:8501")

if __name__ == "__main__":
    main()
