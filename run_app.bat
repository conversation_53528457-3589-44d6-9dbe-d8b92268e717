@echo off
echo ToksNet - Skin Condition Classifier
echo ====================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher
    pause
    exit /b 1
)

REM Check if requirements.txt exists
if not exist requirements.txt (
    echo Error: requirements.txt not found
    pause
    exit /b 1
)

REM Install requirements
echo Installing requirements...
pip install -r requirements.txt
if errorlevel 1 (
    echo Error: Failed to install requirements
    pause
    exit /b 1
)

REM Check if model file exists
if not exist best_efficientnet.keras (
    echo Warning: Model file 'best_efficientnet.keras' not found
    echo The application may not work correctly
    echo.
)

REM Run the Streamlit app
echo Starting ToksNet application...
echo Open your browser to: http://localhost:8501
echo Press Ctrl+C to stop the application
echo.
streamlit run app.py

pause
