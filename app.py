import streamlit as st
import tensorflow as tf
from tensorflow.keras.preprocessing import image
import numpy as np
from PIL import Image

# Load model and class names
model = tf.keras.models.load_model('best_efficientnet.keras', custom_objects={
    'preprocess_input': tf.keras.applications.efficientnet.preprocess_input
})
class_names = ['acne', 'cancer', 'eczema', 'keratosis', 'milia', 'rosacea']

st.title("🩺 Skin Condition Classifier")
st.markdown("Upload a skin image and get the predicted condition.")

uploaded_file = st.file_uploader("Choose an image...", type=["jpg", "jpeg", "png"])

if uploaded_file is not None:
    img = Image.open(uploaded_file).convert('RGB')
    st.image(img, caption='Uploaded Image', use_column_width=True)

    # Preprocess image
    img = img.resize((224, 224))
    img_array = image.img_to_array(img)
    img_array = tf.keras.applications.efficientnet.preprocess_input(img_array)
    img_array = tf.expand_dims(img_array, 0)

        # Predict
    prediction = model.predict(img_array)
    predicted_class = class_names[np.argmax(prediction)]
    confidence = np.max(tf.nn.softmax(prediction)) * 100

    # Output
    st.subheader("🔍 Prediction:")
    st.markdown(f"**{predicted_class.upper()}** with **{confidence:.2f}%** confidence")

    # Top 3 predictions
    st.subheader("📊 Top 3 Predictions:")
    sorted_indices = np.argsort(prediction[0])[::-1][:3]
    for i in sorted_indices:
        st.markdown(f"- {class_names[i]}: {prediction[0][i]*100:.2f}%")
