#!/bin/bash

echo "ToksNet - Skin Condition Classifier"
echo "===================================="
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Error: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.7 or higher"
    exit 1
fi

# Check Python version
python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "✅ Python version: $python_version"

# Check if requirements.txt exists
if [ ! -f "requirements.txt" ]; then
    echo "❌ Error: requirements.txt not found"
    exit 1
fi

# Install requirements
echo "📦 Installing requirements..."
pip3 install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to install requirements"
    exit 1
fi

# Check if model file exists
if [ ! -f "best_efficientnet.keras" ]; then
    echo "⚠️  Warning: Model file 'best_efficientnet.keras' not found"
    echo "The application may not work correctly"
    echo
fi

# Run the Streamlit app
echo "🚀 Starting ToksNet application..."
echo "📱 Open your browser to: http://localhost:8501"
echo "🛑 Press Ctrl+C to stop the application"
echo

streamlit run app.py
