# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Jupyter Notebook
.ipynb_checkpoints

# Model files (if you want to exclude large model files)
# *.keras
# *.h5
# *.pb

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp

# Streamlit
.streamlit/

# Data files (uncomment if you want to exclude dataset)
# Skin_Conditions/
# *.zip
